"""智能ID生成器 - 为FuzzLM系统提供有意义的ID生成功能

支持语义化ID生成, 提高可读性和调试体验
"""

import re
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Union


class IDGenerator:
    """智能ID生成器"""

    def __init__(self, config: Union[dict[str, Any], None] = None):
        """初始化ID生成器

        Args:
            config: 配置字典, 包含id_generation配置

        """
        self.config = config or {}
        self.id_config = self.config.get("id_generation", {})

    def generate_campaign_id(self, target_path: str) -> str:
        """生成Campaign ID

        Args:
            target_path: 目标程序路径

        Returns:
            生成的Campaign ID, 格式: campaign_MMDD_HHMM_<target>_<uuid6>
            例如: campaign_1229_1430_test_program_a1b2c3

        """
        format_type = self.id_config.get("campaign_format", "semantic")

        # 如果配置为使用原始UUID格式
        if format_type == "uuid":
            return str(uuid.uuid4())

        # 语义化格式生成
        timestamp = datetime.now().strftime("%m%d_%H%M")
        target_name = self._extract_target_name(target_path)
        uuid_length = self.id_config.get("uuid_length", 6)
        short_uuid = uuid.uuid4().hex[:uuid_length]

        return f"campaign_{timestamp}_{target_name}_{short_uuid}"

    def generate_fuzzer_id(self, fuzzer_type: str = "champion") -> str:
        """生成Fuzzer实例ID

        Args:
            fuzzer_type: Fuzzer类型, 如"champion"

        Returns:
            生成的Fuzzer ID, 格式: fuzzer_<type>_<HHMM>_<uuid4>
            例如: fuzzer_champion_1430_a1b2

        """
        format_type = self.id_config.get("fuzzer_format", "semantic")

        # 如果配置为使用原始UUID格式
        if format_type == "uuid":
            return f"fuzzer_{uuid.uuid4().hex[:8]}"

        # 语义化格式生成
        timestamp = datetime.now().strftime("%H%M")
        short_uuid = uuid.uuid4().hex[:4]

        return f"fuzzer_{fuzzer_type}_{timestamp}_{short_uuid}"

    def generate_shadow_id(self) -> str:
        """生成Shadow Fuzzer ID

        Returns:
            生成的Shadow ID, 格式: shadow_<MMSS>_<uuid4>
            例如: shadow_3052_b3c4

        """
        format_type = self.id_config.get("shadow_format", "semantic")

        # 如果配置为使用原始UUID格式
        if format_type == "uuid":
            return f"shadow_{uuid.uuid4().hex[:8]}"

        # 语义化格式生成
        timestamp = datetime.now().strftime("%M%S")
        short_uuid = uuid.uuid4().hex[:4]

        return f"shadow_{timestamp}_{short_uuid}"

    def _extract_target_name(self, target_path: str) -> str:
        """从目标路径提取有意义的名称

        Args:
            target_path: 目标程序路径

        Returns:
            清理后的目标名称, 限制长度为15字符

        """
        try:
            # 提取文件名(不含扩展名)
            target_stem = Path(target_path).stem

            # 清理特殊字符, 只保留字母数字下划线
            clean_name = re.sub(r"[^a-zA-Z0-9_]", "", target_stem)

            # 如果清理后为空, 使用默认名称
            if not clean_name:
                clean_name = "target"

            # 限制长度避免路径过长
            return clean_name[:15]

        except Exception:
            # 出错时返回默认名称
            return "target"


# 全局ID生成器实例
_global_generator: Union[IDGenerator, None] = None


def get_id_generator(config: Union[dict[str, Any], None] = None) -> IDGenerator:
    """获取全局ID生成器实例

    Args:
        config: 可选的配置字典

    Returns:
        ID生成器实例

    """
    global _global_generator

    if _global_generator is None or config is not None:
        _global_generator = IDGenerator(config)

    return _global_generator


def generate_campaign_id(
    target_path: str, config: Union[dict[str, Any], None] = None
) -> str:
    """便捷函数: 生成Campaign ID"""
    generator = get_id_generator(config)
    return generator.generate_campaign_id(target_path)


def generate_fuzzer_id(
    fuzzer_type: str = "champion",
    config: Union[dict[str, Any], None] = None,
) -> str:
    """便捷函数: 生成Fuzzer ID"""
    generator = get_id_generator(config)
    return generator.generate_fuzzer_id(fuzzer_type)


def generate_shadow_id(config: Union[dict[str, Any], None] = None) -> str:
    """便捷函数: 生成Shadow ID"""
    generator = get_id_generator(config)
    return generator.generate_shadow_id()
