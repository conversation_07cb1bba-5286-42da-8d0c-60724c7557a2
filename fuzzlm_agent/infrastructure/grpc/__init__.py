"""gRPC generated code and client implementations."""

# 导出protobuf生成的模块
from . import (
    fuzzing_control_pb2,
    fuzzing_control_pb2_grpc,
    llm_generation_pb2,
    llm_generation_pb2_grpc,
)

# Re-export specific classes for easier access and type checking
from .fuzzing_control_pb2 import (
    FuzzerType,
    HealthCheckRequest,
    HealthCheckResponse,
    InstanceStatus,
    PerformanceMetrics,
    PromoteShadowRequest,
    PromoteShadowResponse,
    SpawnShadowRequest,
    SpawnShadowResponse,
    StartFuzzerRequest,
    StartFuzzerResponse,
    StopFuzzerRequest,
    StopFuzzerResponse,
    StrategyConfig,
    SystemMetrics,
    UpdateStrategyRequest,
    UpdateStrategyResponse,
    ValidateCodeRequest,
    ValidateCodeResponse,
)
from .fuzzing_control_pb2_grpc import (
    FuzzingControlServicer,
    FuzzingControlStub,
)

__all__ = [
    # Modules
    "fuzzing_control_pb2",
    "fuzzing_control_pb2_grpc",
    "llm_generation_pb2",
    "llm_generation_pb2_grpc",
    # Re-exported classes
    "FuzzerType",
    "HealthCheckRequest",
    "HealthCheckResponse",
    "InstanceStatus",
    "PerformanceMetrics",
    "PromoteShadowRequest",
    "PromoteShadowResponse",
    "SpawnShadowRequest",
    "SpawnShadowResponse",
    "StartFuzzerRequest",
    "StartFuzzerResponse",
    "StopFuzzerRequest",
    "StopFuzzerResponse",
    "StrategyConfig",
    "SystemMetrics",
    "UpdateStrategyRequest",
    "UpdateStrategyResponse",
    "ValidateCodeRequest",
    "ValidateCodeResponse",
    "FuzzingControlServicer",
    "FuzzingControlStub",
]
