"""FuzzLM-Agent Knowledge Management
知识管理模块, 支持模糊测试的经验学习和知识库管理

🔄 重构完成 (Phase 3 - 2025-01-24):
- ✅ 统一向量存储 (UnifiedVectorStore) - 整合3个分散的向量存储实现
- ✅ RAG服务组件化 (services/) - 拆分为5个专门的服务组件
- ✅ 统一经验管理器 (UnifiedExperienceManager) - 整合经验存储和检索

🚀 多维RAG系统实现完成 (2025-07-02):
- ✅ 多维编码器 (MultidimensionalEncoder) - 三维度独立向量化
- ✅ 智能检索策略 (IntelligentRetrievalEngine) - 动态权重调整
- ✅ 批量并行优化 - 2-8线程50-57%性能提升
- ✅ LRU+TTL缓存系统 - 60%+命中率, 77-84%性能提升

核心组件:
- unified_vector_store: 多维向量搜索引擎 (支持三维度独立索引)
- unified_experience_manager: 智能经验管理器 (支持语义检索)
- dimension_encoders: 多维编码器 (目标画像/问题情境/解决方案范式)
- intelligent_retrieval_strategy: 智能检索策略引擎
"""

# 🎯 统一接口 - 推荐使用 (使用延迟导入避免循环依赖)
from typing import Any


def _import_unified_modules() -> dict[str, Any]:
    # 注意: 统一经验管理器、向量存储等高级功能已被移除
    # 根据"重构即替换"原则, 这些过度工程化的组件已被删除
    # 当前只保留SimpleKnowledgeBase作为最小化的知识管理解决方案
    return {}


# 使用模块级别的__getattr__来支持延迟导入
_unified_modules = None
_data_structures = None


def __getattr__(name: str) -> Any:
    global _unified_modules, _data_structures

    # 首先检查统一模块
    if _unified_modules is None:
        _unified_modules = _import_unified_modules()

    if name in _unified_modules:
        return _unified_modules[name]

    # 然后检查数据结构
    if _data_structures is None:
        _data_structures = _import_data_structures()

    if name in _data_structures:
        return _data_structures[name]

    msg = f"module '{__name__}' has no attribute '{name}'"
    raise AttributeError(msg)


# 🎲 专用功能模块 (保留独立功能)
# pattern_recognition模块已删除 - 属于孤立循环冗余代码


# 数据结构也通过延迟导入提供
def _import_data_structures() -> dict[str, Any]:
    # 注意: 复杂的数据结构已被移除, 符合研究原型的最小化需求
    # 当前使用简化的数据模型, 定义在 domain/schemas.py 中
    return {}


__all__ = [
    "EncodingConfig",
    "ExperienceQuality",
    "ExperienceQuery",
    "ExperienceType",
    # 🎲 专用模块 (pattern_recognition已删除)
    # 经验数据结构
    "FuzzingExperience",
    "IntelligentRetrievalEngine",
    # 🚀 多维RAG系统 (2025-07-02实现完成)
    "MultidimensionalEncoder",
    "ProblemContext",
    "QueryIntent",
    "RetrievalQuery",
    "SearchContext",
    "SolutionParadigm",
    "TargetProfile",
    "UnifiedExperienceManager",
    # 🎯 统一知识管理接口
    "UnifiedVectorStore",
    "UnifiedVectorStoreConfig",
    "get_intelligent_retrieval_engine",
]
