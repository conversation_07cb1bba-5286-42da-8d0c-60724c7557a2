"""Consolidated validation module for FuzzLM orchestrator
===================================================

Provides common validation functions for strategy validation, security patterns,
and dangerous code detection used across the orchestrator components.

This module consolidates validation logic previously scattered across:
- campaign_orchestrator.py: Strategy structure and parameter validation
- phase1_strategy.py: Mutator, scheduler, feedback validation and Rust code validation

Key responsibilities:
- Strategy structure validation
- Mutator configuration validation
- Scheduler and feedback validation
- Security pattern detection
- Rust code safety validation
"""

import logging
import re
from typing import Any, Optional

logger = logging.getLogger(__name__)


# Type aliases for clarity
StrategyDict = dict[str, Any]
ValidationResult = tuple[bool, Optional[str]]  # (is_valid, error_message)


# Valid configuration constants
VALID_MUTATOR_TYPES = {
    "havoc",
    "splice",
    "radamsa",
    "bit_flip",
    "byte_flip",
    "interesting_values",
    "dictionary",
    "crossover",
    "custom",
    # LibAFL specific mutators
    "BitFlipMutator",
    "ByteFlipMutator",
    "HavocMutator",
    "TokenInsertMutator",
    "CrossoverMutator",
    "DictionaryMutator",
    "SpliceMutator",
    "CustomMutator",
}

VALID_SCHEDULER_TYPES = {
    "queue",
    "weighted",
    "adaptive",
    "probabilistic",
}

VALID_FEEDBACK_TYPES = {
    "max_map",
    "coverage",
    "crash",
    "combined",
    "differential",
    "sanitizer",
}

# Security patterns for code validation
DANGEROUS_CODE_PATTERNS = [
    # Pattern, Description
    ("unsafe ", "Unsafe code blocks"),
    ("std::process", "Process spawning"),
    ("std::fs::remove", "File deletion"),
    ("panic!", "Panic macros"),
    ("unreachable!", "Unreachable assertions"),
    ("unimplemented!", "Unimplemented placeholders"),
    ("std::env::set", "Environment manipulation"),
    ("std::fs::write", "File writing without validation"),
    # Additional Rust-specific dangerous patterns
    ("std::process::Command", "Command execution"),
    (r"unsafe\s*\{[^}]*system\(", "Unsafe system calls"),
]

# Performance thresholds
MUTATOR_COUNT_THRESHOLDS = {
    "min": 2,
    "optimal_min": 10,
    "optimal_max": 20,
    "max": 30,
}


def validate_strategy_structure(strategy: StrategyDict) -> ValidationResult:
    """Validate the basic structure of a fuzzing strategy.

    Checks for required fields and proper data types.

    Args:
        strategy: Strategy dictionary to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check required fields
    required_fields = ["mutators", "scheduler", "feedback"]
    missing_fields = [field for field in required_fields if field not in strategy]

    if missing_fields:
        return False, f"Strategy missing required fields: {', '.join(missing_fields)}"

    # Validate mutators structure
    mutators = strategy.get("mutators", [])
    if not isinstance(mutators, list):
        return False, "Mutators must be a list"

    if not mutators:
        return False, "Strategy has no mutators defined"

    # Validate scheduler structure
    scheduler = strategy.get("scheduler", {})
    if not isinstance(scheduler, dict):
        return False, "Scheduler must be a dictionary"

    # Validate feedback structure
    feedback = strategy.get("feedback", {})
    if not isinstance(feedback, dict):
        return False, "Feedback must be a dictionary"

    return True, None


def validate_mutators(
    mutators: list[Any], allow_empty: bool = False
) -> list[dict[str, Any]]:
    """Validate and normalize a list of mutators.

    Args:
        mutators: List of mutator configurations
        allow_empty: Whether to allow empty mutator list

    Returns:
        List of validated and normalized mutator configurations
    """
    validated_mutators = []

    # Handle empty list
    if not mutators:
        if allow_empty:
            return []
        # Return default mutators
        return [
            {"type": "havoc", "config": {"max_len": 1024}},
            {"type": "splice", "config": {}},
            {"type": "bit_flip", "config": {}},
        ]

    for i, mutator in enumerate(mutators):
        if not isinstance(mutator, dict):
            logger.warning(f"Mutator {i} is not a dictionary, skipping")
            continue

        mutator_type = mutator.get("type", "")

        # Normalize type (handle both lowercase and CamelCase)
        normalized_type = (
            mutator_type.lower()
            if mutator_type.lower() in VALID_MUTATOR_TYPES
            else mutator_type
        )

        if normalized_type not in VALID_MUTATOR_TYPES:
            logger.warning(
                f"Unknown mutator type: {mutator_type}. "
                f"Valid types: {', '.join(sorted(VALID_MUTATOR_TYPES))}"
            )
            continue

        validated_mutators.append(
            {"type": normalized_type, "config": mutator.get("config", {})}
        )

    # Ensure at least one mutator
    if not validated_mutators and not allow_empty:
        validated_mutators.append({"type": "havoc", "config": {"max_len": 1024}})

    return validated_mutators


def validate_scheduler(scheduler: Any) -> dict[str, Any]:
    """Validate and normalize scheduler configuration.

    Args:
        scheduler: Scheduler configuration

    Returns:
        Validated and normalized scheduler configuration
    """
    if not isinstance(scheduler, dict):
        return {"type": "weighted", "config": {}}

    scheduler_type = scheduler.get("type", "").lower()
    if scheduler_type not in VALID_SCHEDULER_TYPES:
        logger.warning(
            f"Invalid scheduler type: {scheduler_type}, using weighted. "
            f"Valid types: {', '.join(sorted(VALID_SCHEDULER_TYPES))}"
        )
        scheduler_type = "weighted"

    return {"type": scheduler_type, "config": scheduler.get("config", {})}


def validate_feedback(feedback: Any) -> dict[str, Any]:
    """Validate and normalize feedback configuration.

    Args:
        feedback: Feedback configuration

    Returns:
        Validated and normalized feedback configuration
    """
    if not isinstance(feedback, dict):
        return {"type": "max_map", "config": {}}

    feedback_type = feedback.get("type", "").lower()
    if feedback_type not in VALID_FEEDBACK_TYPES:
        logger.warning(
            f"Invalid feedback type: {feedback_type}, using max_map. "
            f"Valid types: {', '.join(sorted(VALID_FEEDBACK_TYPES))}"
        )
        feedback_type = "max_map"

    return {"type": feedback_type, "config": feedback.get("config", {})}


def validate_strategy_parameters(strategy: StrategyDict) -> list[str]:
    """Validate strategy parameters for performance and reasonableness.

    Args:
        strategy: Strategy dictionary to validate

    Returns:
        List of warning messages (empty if all parameters are reasonable)
    """
    warnings = []

    # Check mutator count
    mutator_count = len(strategy.get("mutators", []))

    if mutator_count > MUTATOR_COUNT_THRESHOLDS["max"]:
        warnings.append(
            f"Strategy has {mutator_count} mutators, which may impact performance. "
            f"Consider reducing to {MUTATOR_COUNT_THRESHOLDS['optimal_min']}-"
            f"{MUTATOR_COUNT_THRESHOLDS['optimal_max']} mutators for optimal performance."
        )
    elif mutator_count < MUTATOR_COUNT_THRESHOLDS["min"]:
        warnings.append(
            f"Strategy has only {mutator_count} mutator(s), "
            f"which may limit exploration. Consider adding more diverse mutators."
        )

    # Log info about configuration
    scheduler = strategy.get("scheduler", {})
    if isinstance(scheduler, dict):
        scheduler_type = scheduler.get("type", "unknown")
        logger.info(f"Using {scheduler_type} scheduler")

    feedback = strategy.get("feedback", {})
    if isinstance(feedback, dict):
        feedback_type = feedback.get("type", "unknown")
        logger.info(f"Using {feedback_type} feedback")

    # Check for performance settings
    if "performance" in strategy:
        perf_settings = strategy["performance"]
        if isinstance(perf_settings, dict):
            logger.info(f"Performance settings: {perf_settings}")

    return warnings


def check_dangerous_patterns(
    code: str, pattern_list: Optional[list[tuple[str, str]]] = None
) -> list[str]:
    """Check code for dangerous patterns.

    Args:
        code: Code to check
        pattern_list: Optional custom pattern list, defaults to DANGEROUS_CODE_PATTERNS

    Returns:
        List of warning messages for found patterns
    """
    if pattern_list is None:
        pattern_list = DANGEROUS_CODE_PATTERNS

    warnings = []

    for pattern, description in pattern_list:
        if pattern in code:
            warnings.append(f"{description} ({pattern})")

    return warnings


def validate_rust_mutator_code(code: str) -> ValidationResult:
    """Validate Rust mutator code for safety and correctness.

    Args:
        code: Rust code to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not code or len(code) < 50:
        return False, "Code is too short or empty"

    # Check for required structures
    required_patterns = [
        (r"impl.*Mutator", "Mutator trait implementation"),
        (r"fn\s+mutate", "mutate function"),
    ]

    for pattern, description in required_patterns:
        if not re.search(pattern, code, re.IGNORECASE | re.DOTALL):
            return False, f"Missing required: {description}"

    # Check for dangerous patterns
    dangerous_warnings = check_dangerous_patterns(code)
    if dangerous_warnings:
        return False, f"Dangerous patterns found: {'; '.join(dangerous_warnings)}"

    return True, None


def validate_custom_code(
    custom_code: str, rust_mutator_code: Optional[str] = None
) -> list[str]:
    """Validate custom code and optional Rust mutator code.

    Args:
        custom_code: Custom code to validate
        rust_mutator_code: Optional Rust mutator code to validate

    Returns:
        List of warning messages
    """
    warnings = []

    if custom_code:
        logger.info("Validating custom code...")
        code_warnings = check_dangerous_patterns(custom_code)
        if code_warnings:
            warnings.extend([f"Custom code: {w}" for w in code_warnings])

    if rust_mutator_code:
        logger.info("Found Rust mutator code in strategy")
        rust_warnings = check_dangerous_patterns(rust_mutator_code)
        if rust_warnings:
            warnings.extend([f"Rust mutator: {w}" for w in rust_warnings])

    return warnings


def looks_like_rust_code(code: str) -> bool:
    """Check if code appears to be Rust code.

    Args:
        code: Code to check

    Returns:
        True if code contains Rust indicators
    """
    rust_indicators = [
        "impl",
        "fn",
        "let",
        "mut",
        "pub",
        "struct",
        "trait",
        "use",
        "mod",
        "->",
        "&",
        "Vec<",
        "Result<",
        "Option<",
        "match",
        "enum",
        "async",
        "await",
        "move",
        "::",
    ]

    # Count how many indicators are present
    indicator_count = sum(1 for indicator in rust_indicators if indicator in code)

    # Require at least 3 indicators for higher confidence
    return indicator_count >= 3


def extract_rust_code_from_response(response: str) -> str:
    """Extract Rust code from an LLM response.

    Args:
        response: LLM response text that may contain code blocks

    Returns:
        Extracted Rust code or empty string if not found
    """
    # Look for Rust code blocks first
    if "```rust" in response:
        start = response.find("```rust") + 7
        end = response.find("```", start)
        if end > start:
            return response[start:end].strip()

    # Look for generic code blocks
    if "```" in response:
        import re

        code_blocks = re.findall(r"```(?:\w+)?\n?(.*?)```", response, re.DOTALL)

        for block in code_blocks:
            if looks_like_rust_code(block):
                return str(block).strip()

    # If no code blocks, check if the whole response is code
    if looks_like_rust_code(response):
        return response.strip()

    return ""


def create_validation_report(strategy: StrategyDict) -> dict[str, Any]:
    """Create a comprehensive validation report for a strategy.

    Args:
        strategy: Strategy to validate

    Returns:
        Validation report with structure, parameters, and security analysis
    """
    report: dict[str, Any] = {
        "is_valid": True,
        "structure_validation": None,
        "parameter_warnings": [],
        "security_warnings": [],
        "summary": "",
    }

    # Validate structure
    is_valid, error_msg = validate_strategy_structure(strategy)
    if not is_valid:
        report["is_valid"] = False
        report["structure_validation"] = error_msg
        report["summary"] = f"Invalid strategy structure: {error_msg}"
        return report

    # Validate parameters
    param_warnings = validate_strategy_parameters(strategy)
    report["parameter_warnings"] = param_warnings

    # Check for custom code
    if "custom_code" in strategy or "rust_mutator_code" in strategy:
        security_warnings = validate_custom_code(
            strategy.get("custom_code", ""), strategy.get("rust_mutator_code")
        )
        report["security_warnings"] = security_warnings

    # Generate summary
    if report["parameter_warnings"] or report["security_warnings"]:
        report["summary"] = "Strategy validated with warnings"
    else:
        report["summary"] = "Strategy validated successfully"

    return report
